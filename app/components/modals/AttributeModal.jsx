'use client';
import React, { useState } from 'react';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import ToggleSwitch from '../Inputs/ToggleSwitch';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import ImageUpload from '../Inputs/ImageUpload';

const AttributeModal = ({
  isOpen,
  onClose,
  formData,
  setFormData,
  error,
  valueFormData,
  setValueFormData,
  handleSubmit,
  handleAddValue,
  typeOptions,
}) => {
  const [images, setImages] = useState([]);

  return (
    <BaseOffCanvas isOpen={isOpen} onClose={onClose} title="Add Attribute" size="sm">
      <form onSubmit={handleSubmit} className="h-full">
        <div className="p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          <InputField
            label="Attribute Name"
            placeholder="Enter attribute name"
            value={formData.attributeName}
            onChange={(e) =>
              setFormData({ ...formData, attributeName: e.target.value })
            }
            required={false}
          />

          <div className="flex flex-col mb-4">
            <span className="form-label">
              Type
            </span>
            <SelectField
              className="single-select"
              placeholder="Select type"
              options={typeOptions}
              value={formData.type}
              onChange={(selectedOption) =>
                setFormData({ ...formData, type: selectedOption.value })
              }
            />
          </div>

          <div className="space-y-3 flex flex-col">
            <CustomCheckbox
              id="displayInFilters"
              name="displayInFilters"
              label="Display in Filters"
              // checked={formData.displayInFilters}
              onChange={(checked) =>
                setFormData({ ...formData, displayInFilters: checked })
              }
            />

            <CustomCheckbox
              id="useInComparison"
              name="useInComparison"
              label="Use in Comparison"
              // checked={formData.useInComparison}
              onChange={(checked) =>
                setFormData({ ...formData, useInComparison: checked })
              }
            />

            <CustomCheckbox
              id="requiredForProducts"
              name="requiredForProducts"
              label="Required for Products"
              // checked={formData.requiredForProducts}
              onChange={(checked) =>
                setFormData({ ...formData, requiredForProducts: checked })
              }
            />

            <div className="mb-0">
              <label className="block text-sm font-medium mb-1">
                Status
              </label>
              <ToggleSwitch
                checked={formData.status}
                onChange={(e) => setFormData({...formData, status: e.target.checked})}
              />
            </div>
          </div>

          <div className="border-t border-border-color pt-4 mt-4">
            <h4 className="text-sm font-semibold mb-3">Attribute Values</h4>

            <div className="space-y-3">
              <InputField
                label="Value Label"
                placeholder="Enter value label"
                value={valueFormData.label}
                onChange={(e) =>
                  setValueFormData({ ...valueFormData, label: e.target.value })
                }
              />
              {error && <span className="text-danger-500 text-xs">{error}</span>}

              <InputField
                label="Value Code"
                placeholder="Enter value code"
                value={valueFormData.code}
                onChange={(e) =>
                  setValueFormData({ ...valueFormData, code: e.target.value })
                }
              />

              {formData.type === 'color' && (
                <InputField
                  type="color"
                  label="Color"
                  value={valueFormData.value}
                  onChange={(e) =>
                    setValueFormData({ ...valueFormData, value: e.target.value })
                  }
                />
              )}

              {formData.type === 'image' && (
                // <InputField
                //   type="file"
                //   label="Image"
                //   accept="image/*"
                //   onChange={(e) =>
                //     setValueFormData({
                //       ...valueFormData,
                //       value: e.target.files[0],
                //     })
                //   }
                // />
                <ImageUpload
                  heading="Image"
                  value={valueFormData.value}
                  onChange={(e) =>
                    setValueFormData({ ...valueFormData, value: e.target.files[0] })
                  }
                  setImages={setImages}
                  maxFiles={1}
                  maxSize={3000000}
                  tooltipContent={null}
                  showFileInfo={false}
                  className="!p-0"
                />
              )}

              <div className="flex justify-end">
                <button
                  type="button"
                  className="btn btn-outline-primary !text-xs inline-flex gap-1 items-center !px-2 group"
                  onClick={handleAddValue}
                >
                  <span className="icon icon-plus group-hover:text-white transition-base" />
                  Add Value
                </button>
              </div>

              {formData.values.length > 0 && (
                <div className="mt-4 border-t border-border-color pt-4">
                  <h5 className="text-sm font-medium mb-2">Added Values:</h5>
                  <div className="space-y-2">
                    {formData.values.map((value, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                      >
                        <span>{value.label}</span>
                        <button
                          type="button"
                          className="text-danger-500 hover:text-danger-600"
                          onClick={() =>
                            setFormData({
                              ...formData,
                              values: formData.values.filter(
                                (_, i) => i !== index
                              ),
                            })
                          }
                        >
                          <span className="icon icon-trash" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
          >
            Cancel
          </button>
          <button type="submit" className="btn btn-primary">
            Save Attribute
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default AttributeModal;
