'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';


const InfoRow = ({ label, value, className }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm text-dark-500 font-medium">{value}</span>
  </div>
);

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const ReturnDetailModal = ({ isOpen, onClose, returnRequest }) => {
  if (!returnRequest) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Return Request - ${returnRequest.returnId}`}
      size="md"
    >
      <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Return Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Return Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Return ID" value={returnRequest.returnId} />
            <InfoRow label="Order ID" value={returnRequest.orderId} />
            <InfoRow label="Customer Name" value={returnRequest.customerName} />
            <InfoRow label="Request Date" value={new Date(returnRequest.requestDate).toLocaleDateString()} />
          </div>
        </div>
        <hr/>

        {/* Product Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Product Information</h4>
          <div className="space-y-3">
            {returnRequest.products.map((product, index) => (
              <div key={index} className="flex justify-between items-center p-3  border border-border-color rounded-lg">
                <div>
                  <p className="font-medium">{product.name}</p>
                  <p className="text-xs text-gray-400">SKU: {product.sku}</p>
                  <p className="text-xs text-gray-400">Quantity: {product.quantity}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-400">${product.price.toFixed(2)}</p>
                  <p className=" font-semibold">Total: ${(product.price * product.quantity).toFixed(2)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
        <hr/>

        {/* Return Details */}
        <div className="">
          <h4 className="font-semibold mb-3">Return Details</h4>
          <div className="space-y-3">
            <InfoRow label="Return Reason" value={returnRequest.returnReason} />
            <InfoRow label="Customer Comments" value={returnRequest.customerComments} />
            <InfoRow label="Refund Method" value={returnRequest.refundMethod} />
            {returnRequest.adminNotes && (
              <InfoRow label="Admin Notes" value={returnRequest.adminNotes} />
            )}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        <button className="btn btn-primary">Approve Return</button>
        <button className="btn btn-outline-danger">Reject Return</button>
        <button className="btn btn-outline-gray">Process Refund</button>
        <button className="btn btn-outline-gray">Request Replacement</button>
      </div>
    </BaseOffCanvas>
  );
};

const ProductReturns = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedReturn, setSelectedReturn] = useState(null);
  const [isReturnDetailOpen, setIsReturnDetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    returnId: true,
    orderId: true,
    customerName: true,
    returnReason: true,
    products: true,
    returnStatus: true,
    refundMethod: true,
    requestDate: true,
    processedBy: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [returnRequests] = useState([
    {
      id: 'RET-001',
      returnId: 'RET-001',
      orderId: '#HSU-19384',
      customerName: 'John Smith',
      returnReason: 'Damaged Product',
      products: [
        { name: 'Wireless Headphones', sku: 'WH-001', quantity: 1, price: 299.99 }
      ],
      returnStatus: 'Requested',
      refundMethod: 'Original Payment',
      requestDate: '2024-01-15T10:30:00Z',
      processedBy: null,
      customerComments: 'The product arrived with a cracked case and the left speaker is not working properly.',
      adminNotes: null
    },
    {
      id: 'RET-002',
      returnId: 'RET-002',
      orderId: '#HSU-19385',
      customerName: 'Sarah Johnson',
      returnReason: 'Not as Described',
      products: [
        { name: 'Bluetooth Speaker', sku: 'BS-002', quantity: 1, price: 156.50 }
      ],
      returnStatus: 'Approved',
      refundMethod: 'Store Credit',
      requestDate: '2024-01-14T09:15:00Z',
      processedBy: 'Admin User',
      customerComments: 'The color is different from what was shown in the product images.',
      adminNotes: 'Approved for store credit as per policy.'
    },
    {
      id: 'RET-003',
      returnId: 'RET-003',
      orderId: '#HSU-19386',
      customerName: 'Mike Davis',
      returnReason: 'Wrong Item',
      products: [
        { name: 'USB Cable', sku: 'UC-003', quantity: 2, price: 19.99 }
      ],
      returnStatus: 'Completed',
      refundMethod: 'Replacement',
      requestDate: '2024-01-13T08:45:00Z',
      processedBy: 'Admin User',
      customerComments: 'Received USB-C cable instead of Lightning cable.',
      adminNotes: 'Replacement sent. Original item returned to inventory.'
    },
    {
      id: 'RET-004',
      returnId: 'RET-004',
      orderId: '#HSU-19387',
      customerName: 'Emily Wilson',
      returnReason: 'Defective',
      products: [
        { name: 'Smart Watch', sku: 'SW-004', quantity: 1, price: 445.75 }
      ],
      returnStatus: 'Rejected',
      refundMethod: 'N/A',
      requestDate: '2024-01-12T16:20:00Z',
      processedBy: 'Admin User',
      customerComments: 'Watch screen is flickering and touch is not responsive.',
      adminNotes: 'Rejected - damage appears to be user-caused based on photos provided.'
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'requested':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-info-500/20 text-info-500';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Return ID',
      selector: (row) => row.returnId,
      sortable: true,
      omit: !displayProperties.returnId,
    },
    {
      name: 'Order ID',
      selector: (row) => row.orderId,
      sortable: true,
      omit: !displayProperties.orderId,
    },
    {
      name: 'Customer Name',
      selector: (row) => row.customerName,
      sortable: true,
      omit: !displayProperties.customerName,
    },
    {
      name: 'Return Reason',
      selector: (row) => row.returnReason,
      sortable: true,
      omit: !displayProperties.returnReason,
    },
    {
      name: 'Product(s)',
      selector: (row) => row.products,
      omit: !displayProperties.products,
      cell: (row) => (
        <div className="text-sm">
          {row.products.map((product, index) => (
            <div key={index}>
              {product.name} (x{product.quantity})
            </div>
          ))}
        </div>
      ),
    },
    {
      name: 'Return Status',
      selector: (row) => row.returnStatus,
      sortable: true,
      omit: !displayProperties.returnStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold text-nowrap rounded-full ${getStatusColor(row.returnStatus)}`}>
          {row.returnStatus}
        </span>
      ),
    },
    {
      name: 'Refund Method',
      selector: (row) => row.refundMethod,
      sortable: true,
      omit: !displayProperties.refundMethod,
    },
    {
      name: 'Request Date',
      selector: (row) => row.requestDate,
      sortable: true,
      omit: !displayProperties.requestDate,
      cell: (row) => new Date(row.requestDate).toLocaleDateString(),
    },
    {
      name: 'Processed By',
      selector: (row) => row.processedBy,
      sortable: true,
      omit: !displayProperties.processedBy,
      cell: (row) => row.processedBy || '-',
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-1">
          <button
            onClick={() => handleViewReturn(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          {row.returnStatus === 'Requested' && (
            <>
              <button 
                data-tooltip-id="approve-tooltip"
                data-tooltip-content="Approve"
                className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-green-500/10 hover:text-green-500 rounded-lg cursor-pointer transition-base">
                <span className="icon icon-check-3 text-base" />
              </button>
              <button 
                data-tooltip-id="reject-tooltip"
                data-tooltip-content="Reject"
                className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base">
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}
          {row.returnStatus === 'Approved' && (
            <button className="text-blue-500 hover:text-blue-600 font-medium text-sm">
              <span className="icon icon-refresh-ccw text-base" />
            </button>
          )}

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="approve-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="reject-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="suspend-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
      width: '130px',
    },
  ];

  const handleViewReturn = (returnRequest) => {
    setSelectedReturn(returnRequest);
    setIsReturnDetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredReturns = returnRequests.filter((returnRequest) =>
    returnRequest.customerName.toLowerCase().includes(filterText.toLowerCase()) ||
    returnRequest.returnId.toLowerCase().includes(filterText.toLowerCase()) ||
    returnRequest.orderId.toLowerCase().includes(filterText.toLowerCase()) ||
    returnRequest.returnReason.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium', grow: 1 },
              { size: 'medium' },
              { size: 'medium', grow: 1 },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredReturns}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Return Detail Modal */}
      <ReturnDetailModal
        isOpen={isReturnDetailOpen}
        onClose={() => setIsReturnDetailOpen(false)}
        returnRequest={selectedReturn}
      />
    </div>
  );
};

export default ProductReturns;
