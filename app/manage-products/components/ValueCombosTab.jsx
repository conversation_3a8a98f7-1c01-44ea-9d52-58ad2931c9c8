'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import FilterField from '../../components/table/FilterField';
import ValueComboDetailModal from './ValueComboDetailModal';
import AddValueComboModal from './AddValueComboModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const ValueCombosTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedCombo, setSelectedCombo] = useState(null);
  const [isComboDetailOpen, setIsComboDetailOpen] = useState(false);
  const [isAddComboOpen, setIsAddComboOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);

  // Sample data for value combos
  const [valueCombos, setValueCombos] = useState([
    {
      id: 'VC-001',
      comboName: 'Tech Essentials Bundle',
      comboCode: 'TEB-001',
      comboType: 'Fixed Bundle',
      status: 'Published',
      totalProducts: 3,
      originalPrice: 299.97,
      comboPrice: 249.99,
      discount: 16.7,
      inventory: 25,
      products: [
        { id: 'PRD-001', name: 'Wireless Bluetooth Headphones', price: 149.99, quantity: 1 },
        { id: 'PRD-004', name: 'Smartphone Case', price: 15.99, quantity: 1 },
        { id: 'PRD-006', name: 'Wireless Charger', price: 39.99, quantity: 1 }
      ],
      tags: ['electronics', 'bundle', 'tech'],
      thumbnail: '/images/tech-bundle.jpg',
      shortDescription: 'Complete tech essentials for modern lifestyle',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:20:00Z'
    },
    {
      id: 'VC-002',
      comboName: 'Home Office Setup',
      comboCode: 'HOS-002',
      comboType: 'Mix & Match',
      status: 'Published',
      totalProducts: 4,
      originalPrice: 599.96,
      comboPrice: 499.99,
      discount: 16.7,
      inventory: 15,
      products: [
        { id: 'PRD-003', name: 'Office Chair Bundle', price: 349.99, quantity: 1 },
        { id: 'PRD-007', name: 'Desk Lamp', price: 79.99, quantity: 1 },
        { id: 'PRD-008', name: 'Desk Organizer', price: 29.99, quantity: 1 },
        { id: 'PRD-009', name: 'Mouse Pad', price: 19.99, quantity: 1 }
      ],
      tags: ['office', 'furniture', 'productivity'],
      thumbnail: '/images/office-setup.jpg',
      shortDescription: 'Everything you need for a productive home office',
      createdAt: '2024-01-12T11:00:00Z',
      updatedAt: '2024-01-19T13:30:00Z'
    },
    {
      id: 'VC-003',
      comboName: 'Summer Fashion Pack',
      comboCode: 'SFP-003',
      comboType: 'Variable Bundle',
      status: 'Draft',
      totalProducts: 5,
      originalPrice: 149.95,
      comboPrice: 119.99,
      discount: 20.0,
      inventory: 50,
      products: [
        { id: 'PRD-002', name: 'Cotton T-Shirt', price: 24.99, quantity: 2 },
        { id: 'PRD-010', name: 'Summer Shorts', price: 34.99, quantity: 1 },
        { id: 'PRD-011', name: 'Canvas Sneakers', price: 59.99, quantity: 1 },
        { id: 'PRD-012', name: 'Baseball Cap', price: 19.99, quantity: 1 }
      ],
      tags: ['fashion', 'summer', 'casual'],
      thumbnail: '/images/summer-fashion.jpg',
      shortDescription: 'Complete summer wardrobe essentials',
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-21T10:15:00Z'
    },
    {
      id: 'VC-004',
      comboName: 'Gaming Starter Kit',
      comboCode: 'GSK-004',
      comboType: 'Fixed Bundle',
      status: 'Pending Approval',
      totalProducts: 4,
      originalPrice: 199.96,
      comboPrice: 159.99,
      discount: 20.0,
      inventory: 30,
      products: [
        { id: 'PRD-013', name: 'Gaming Mouse', price: 49.99, quantity: 1 },
        { id: 'PRD-014', name: 'Gaming Keyboard', price: 79.99, quantity: 1 },
        { id: 'PRD-015', name: 'Gaming Headset', price: 59.99, quantity: 1 },
        { id: 'PRD-016', name: 'Mouse Pad XL', price: 29.99, quantity: 1 }
      ],
      tags: ['gaming', 'accessories', 'starter'],
      thumbnail: '/images/gaming-kit.jpg',
      shortDescription: 'Essential gaming accessories for beginners',
      createdAt: '2024-01-16T09:45:00Z',
      updatedAt: '2024-01-22T15:30:00Z'
    },
    {
      id: 'VC-005',
      comboName: 'Kitchen Essentials',
      comboCode: 'KE-005',
      comboType: 'Mix & Match',
      status: 'Archive',
      totalProducts: 6,
      originalPrice: 249.94,
      comboPrice: 199.99,
      discount: 20.0,
      inventory: 8,
      products: [
        { id: 'PRD-017', name: 'Non-stick Pan Set', price: 89.99, quantity: 1 },
        { id: 'PRD-018', name: 'Kitchen Knife Set', price: 59.99, quantity: 1 },
        { id: 'PRD-019', name: 'Cutting Board', price: 24.99, quantity: 1 },
        { id: 'PRD-020', name: 'Measuring Cups', price: 19.99, quantity: 1 },
        { id: 'PRD-021', name: 'Kitchen Towels', price: 14.99, quantity: 2 }
      ],
      tags: ['kitchen', 'cooking', 'essentials'],
      thumbnail: '/images/kitchen-essentials.jpg',
      shortDescription: 'Must-have kitchen tools for home cooking',
      createdAt: '2024-01-08T16:45:00Z',
      updatedAt: '2024-01-16T12:00:00Z'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Combos', count: valueCombos.length },
    { id: 'Published', label: 'Published', count: valueCombos.filter(c => c.status === 'Published').length },
    { id: 'Draft', label: 'Draft', count: valueCombos.filter(c => c.status === 'Draft').length },
    { id: 'Pending Approval', label: 'Pending Approval', count: valueCombos.filter(c => c.status === 'Pending Approval').length },
    { id: 'Archive', label: 'Archive', count: valueCombos.filter(c => c.status === 'Archive').length }
  ];

  const filteredCombos = valueCombos.filter(combo => {
    const matchesSearch = combo.comboName.toLowerCase().includes(filterText.toLowerCase()) ||
                         combo.comboCode.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || combo.status === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  const columns = [
    {
      name: 'Combo Details',
      selector: row => row.comboName,
      sortable: true,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="icon icon-layers text-gray-400" />
          </div>
          <div>
            <div className="font-medium text-sm">{row.comboName}</div>
            <div className="text-xs text-gray-500">{row.comboCode}</div>
          </div>
        </div>
      ),
      // width: '250px',
    },
    {
      name: 'Type',
      selector: row => row.comboType,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full text-nowrap ${
          row.comboType === 'Fixed Bundle' ? 'bg-info-500/20 text-info-500' :
          row.comboType === 'Mix & Match' ? 'bg-purple-100 text-purple-800' :
          'bg-orange-100 text-orange-800'
        }`}>
          {row.comboType}
        </span>
      ),
      width: '140px',
    },
    {
      name: 'Products',
      selector: row => row.totalProducts,
      sortable: true,
      cell: (row) => (
        <span className="text-sm font-medium">
          {row.totalProducts} items
        </span>
      ),
      // width: '100px',
    },
    {
      name: 'Pricing',
      selector: row => row.comboPrice,
      sortable: true,
      cell: (row) => (
        <div className="text-sm">
          <div className="font-medium">${row.comboPrice}</div>
          <div className="text-xs text-gray-500 line-through">${row.originalPrice}</div>
          <div className="text-xs text-green-600 font-medium">{row.discount}% off</div>
        </div>
      ),
      // width: '120px',
    },
    {
      name: 'Inventory',
      selector: row => row.inventory,
      sortable: true,
      cell: (row) => (
        <span className={`font-medium ${
          row.inventory < 10 ? 'text-red-600' : 
          row.inventory < 25 ? 'text-yellow-600' : 
          'text-green-600'
        }`}>
          {row.inventory}
        </span>
      ),
      // width: '100px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full text-nowrap ${
          row.status === 'Published' ? 'bg-green-100 text-green-800' :
          row.status === 'Draft' ? 'bg-gray-100 text-gray-800' :
          row.status === 'Pending Approval' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {row.status}
        </span>
      ),
      width: '140px',
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewCombo(row)}
            data-tooltip-id="view-combo-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <button
            onClick={() => handleEditCombo(row)}
            data-tooltip-id="edit-combo-tooltip"
            data-tooltip-content="Edit Combo"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-pencil-line text-base" />
          </button>
          <button
            onClick={() => handleCloneCombo(row)}
            data-tooltip-id="clone-combo-tooltip"
            data-tooltip-content="Clone Combo"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-info-500/10 hover:text-info-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-copy text-base" />
          </button>
          <button
            onClick={() => handleDeleteCombo(row.id)}
            data-tooltip-id="delete-combo-tooltip"
            data-tooltip-content="Delete Combo"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-trash text-base" />
          </button>

          <Tooltip id="view-combo-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-combo-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="clone-combo-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-combo-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      width: '160px',
    },
  ];

  const handleViewCombo = (combo) => {
    setSelectedCombo(combo);
    setIsComboDetailOpen(true);
  };

  const handleEditCombo = (combo) => {
    console.log('Edit combo:', combo);
    // Implement edit functionality
  };

  const handleCloneCombo = (combo) => {
    const clonedCombo = {
      ...combo,
      id: `VC-${Date.now()}`,
      comboName: `${combo.comboName} (Copy)`,
      comboCode: `${combo.comboCode}-COPY`,
      status: 'Draft'
    };
    setValueCombos(prev => [...prev, clonedCombo]);
    console.log('Combo cloned:', clonedCombo);
  };

  const handleDeleteCombo = (comboId) => {
    if (window.confirm('Are you sure you want to delete this value combo?')) {
      setValueCombos(prev => prev.filter(c => c.id !== comboId));
      console.log('Combo deleted:', comboId);
    }
  };

  const handleAddCombo = (comboData) => {
    const newCombo = {
      ...comboData,
      id: `VC-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setValueCombos(prev => [...prev, newCombo]);
    console.log('Combo added:', newCombo);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Value Combos</h2>
          <span className="text-sm text-gray-300">
            {filteredCombos.length} of {valueCombos.length} combos
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-bold text-lg" />
            Export
          </button>
          <button
            onClick={() => setIsAddComboOpen(true)}
            className="btn btn-primary inline-flex items-center gap-2"
          >
            <span className="icon icon-plus font-bold text-lg" />
            Create Value Combo
          </button>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search value combos..."
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        />

        <DataTable
          columns={columns}
          data={filteredCombos}
          pagination
          paginationPerPage={10}
          selectableRows
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-layers text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No value combos found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={true}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <ValueComboDetailModal
        isOpen={isComboDetailOpen}
        onClose={() => setIsComboDetailOpen(false)}
        combo={selectedCombo}
      />

      <AddValueComboModal
        isOpen={isAddComboOpen}
        onClose={() => setIsAddComboOpen(false)}
        onAddCombo={handleAddCombo}
      />
    </div>
  );
};

export default ValueCombosTab;
