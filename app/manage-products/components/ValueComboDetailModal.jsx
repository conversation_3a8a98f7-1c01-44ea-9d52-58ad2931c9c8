'use client';

import React from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const InfoRow = ({ label, value }) => (
  <div className="flex flex-col gap-1">
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm font-medium">{value || 'N/A'}</span>
  </div>
);

const ValueComboDetailModal = ({ isOpen, onClose, combo }) => {
  if (!combo) return null;

  const totalOriginalPrice = combo.originalPrice;
  const totalComboPrice = combo.comboPrice;
  const totalSavings = totalOriginalPrice - totalComboPrice;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Value Combo Details - ${combo.comboCode}`}
      size="md"
    >
      <div className="space-y-6 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* General Information */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">General Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Combo Name" value={combo.comboName} />
            <InfoRow label="Combo Code" value={combo.comboCode} />
            <InfoRow label="Combo Type" value={combo.comboType} />
            <InfoRow label="Total Products" value={`${combo.totalProducts} items`} />
            <InfoRow label="Status" value={
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                combo.status === 'Published' ? 'bg-green-100 text-green-800' :
                combo.status === 'Draft' ? 'bg-gray-100 text-gray-800' :
                combo.status === 'Pending Approval' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {combo.status}
              </span>
            } />
            <InfoRow label="Inventory" value={
              <span className={`font-medium ${
                combo.inventory < 10 ? 'text-red-600' : 
                combo.inventory < 25 ? 'text-yellow-600' : 
                'text-green-600'
              }`}>
                {combo.inventory} units
              </span>
            } />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Description */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Description</h4>
          <div className="space-y-3">
            <InfoRow label="Short Description" value={combo.shortDescription} />
            {combo.tags && combo.tags.length > 0 && (
              <div className="flex flex-col gap-1">
                <span className="text-sm text-gray-500/60 font-normal">Tags</span>
                <div className="flex flex-wrap gap-2">
                  {combo.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex px-2 py-1 text-xs font-medium bg-info-500/20 text-info-500 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Pricing Information */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Pricing Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Original Total Price" value={`$${totalOriginalPrice.toFixed(2)}`} />
            <InfoRow label="Combo Price" value={`$${totalComboPrice.toFixed(2)}`} />
            <InfoRow label="Total Savings" value={
              <span className="text-green-600 font-semibold">
                ${totalSavings.toFixed(2)} ({combo.discount}% off)
              </span>
            } />
            <InfoRow label="Discount Type" value={`${combo.discount}% Percentage Discount`} />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Included Products */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Included Products</h4>
          <div className="space-y-3">
            {combo.products.map((product, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                    <span className="icon icon-package text-gray-400 text-sm" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{product.name}</div>
                    <div className="text-xs text-gray-500">Qty: {product.quantity}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-sm">${product.price}</div>
                  <div className="text-xs text-gray-500">
                    ${(product.price * product.quantity).toFixed(2)} total
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Pricing Summary */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Individual Products Total:</span>
                <span>${totalOriginalPrice.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm text-green-600">
                <span>Combo Discount ({combo.discount}%):</span>
                <span>-${totalSavings.toFixed(2)}</span>
              </div>
              <hr className="border-blue-200" />
              <div className="flex justify-between font-semibold">
                <span>Combo Price:</span>
                <span>${totalComboPrice.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Combo Configuration */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Combo Configuration</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow 
              label="Combo Type Details" 
              value={
                combo.comboType === 'Fixed Bundle' ? 'All products must be purchased together' :
                combo.comboType === 'Mix & Match' ? 'Customers can choose from available options' :
                'Variable quantities allowed for each product'
              } 
            />
            <InfoRow label="Minimum Purchase" value="All combo items required" />
            <InfoRow label="Inventory Tracking" value="Combined inventory tracking" />
            <InfoRow label="Shipping" value="Ships as single order" />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Performance Metrics */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Performance Metrics</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Total Sales" value="47 combos sold" />
            <InfoRow label="Revenue Generated" value="$11,749.53" />
            <InfoRow label="Average Order Value" value="$249.99" />
            <InfoRow label="Conversion Rate" value="12.3%" />
            <InfoRow label="Customer Rating" value="4.7/5 (23 reviews)" />
            <InfoRow label="Return Rate" value="2.1%" />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Meta Information */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Meta Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow 
              label="Created Date" 
              value={new Date(combo.createdAt).toLocaleDateString()} 
            />
            <InfoRow 
              label="Last Updated" 
              value={new Date(combo.updatedAt).toLocaleDateString()} 
            />
            <InfoRow label="Created By" value="Admin User" />
            <InfoRow label="Last Modified By" value="Admin User" />
          </div>
        </div>

        {/* Marketing Information */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Marketing & Display</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Display on Homepage" value="Yes" />
            <InfoRow label="Featured Combo" value="No" />
            <InfoRow label="Marketing Campaign" value="Summer Sale 2024" />
            <InfoRow label="SEO Title" value={combo.comboName} />
            <InfoRow label="URL Slug" value={combo.comboCode.toLowerCase()} />
            <InfoRow label="Meta Description" value={combo.shortDescription} />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        <button
          onClick={onClose}
          className="btn btn-outline-gray"
        >
          Close
        </button>
        <button className="btn btn-gray">
          Preview Combo
        </button>
        <button className="btn btn-primary">
          Edit Combo
        </button>
      </div>
    </BaseOffCanvas>
  );
};

export default ValueComboDetailModal;
