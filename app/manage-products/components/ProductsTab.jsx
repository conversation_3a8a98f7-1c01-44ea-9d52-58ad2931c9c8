'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import FilterField from '../../components/table/FilterField';
import ProductDetailModal from './ProductDetailModal';
import AddProductModal from './AddProductModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Variant Hover Dropdown Component
const VariantHoverDropdown = ({ variants, isVisible, position, onMouseEnter, onMouseLeave }) => {
  if (!isVisible || !variants || variants.length === 0) return null;

  return (
    <div
      className="absolute z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-72"
      style={{
        top: position.y + 10,
        left: position.x - 144, // Center the dropdown
        transform: 'translateX(-50%)'
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="space-y-3">
        <div className="flex items-center gap-2 pb-2 border-b">
          <span className="icon icon-grid-3x3 text-gray-400 text-sm" />
          <h4 className="font-medium text-sm text-gray-900">
            Product Variants ({variants.length})
          </h4>
        </div>

        <div className="max-h-64 overflow-y-auto space-y-2">
          {variants.map((variant, index) => (
            <div key={index} className="p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-start mb-2">
                <div className="flex-1">
                  <h5 className="font-medium text-sm text-gray-900 truncate">
                    {variant.name}
                  </h5>
                  <p className="text-xs text-gray-500 mt-1">
                    SKU: {variant.sku}
                  </p>
                </div>
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  variant.status === 'Active' ? 'bg-green-100 text-green-800' :
                  variant.status === 'Inactive' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {variant.status}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-500">Price:</span>
                  <span className="font-medium text-green-600">${variant.price}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Stock:</span>
                  <span className={`font-medium ${
                    variant.stock > 10 ? 'text-green-600' :
                    variant.stock > 0 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {variant.stock}
                  </span>
                </div>
              </div>

              {variant.attributes && variant.attributes.length > 0 && (
                <div className="mt-2 pt-2 border-t border-gray-200">
                  <div className="flex flex-wrap gap-1">
                    {variant.attributes.map((attr, attrIndex) => (
                      <span
                        key={attrIndex}
                        className="inline-flex px-1.5 py-0.5 text-xs bg-blue-100 text-blue-700 rounded"
                      >
                        {attr.name}: {attr.value}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';


const ProductsTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isProductDetailOpen, setIsProductDetailOpen] = useState(false);
  const [isAddProductOpen, setIsAddProductOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);

  // Hover dropdown state
  const [hoveredVariants, setHoveredVariants] = useState(null);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
  const [isHoverDropdownVisible, setIsHoverDropdownVisible] = useState(false);
  const hoverTimeoutRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    productName: true,
    productCode: true,
    category: true,
    brand: true,
    productType: true,
    status: true,
    price: true,
    inventory: true,
    variants: true,
  });

  // Sample data - in real app this would come from API
  const [products, setProducts] = useState([
    {
      id: 'PRD-001',
      productName: 'Wireless Bluetooth Headphones',
      productCode: 'WBH-001',
      productType: 'Simple',
      category: { name: 'Electronics', id: 'cat-1' },
      brand: { name: 'TechSound', id: 'brand-1' },
      status: 'Published',
      mrp: 199.99,
      sellingPrice: 149.99,
      costPrice: 89.99,
      inventory: 150,
      minOrderQty: 1,
      variants: 0,
      tags: ['wireless', 'bluetooth', 'audio'],
      thumbnail: '/images/headphones.jpg',
      shortDescription: 'High-quality wireless headphones with noise cancellation',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:20:00Z'
    },
    {
      id: 'PRD-002',
      productName: 'Cotton T-Shirt',
      productCode: 'CTS-002',
      productType: 'Variant',
      category: { name: 'Clothing', id: 'cat-2' },
      brand: { name: 'ComfortWear', id: 'brand-2' },
      status: 'Published',
      mrp: 29.99,
      sellingPrice: 24.99,
      costPrice: 12.99,
      inventory: 500,
      minOrderQty: 1,
      variants: 12,
      variantDetails: [
        {
          name: 'Red - Small',
          sku: 'CTS-002-RED-S',
          price: 24.99,
          stock: 45,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Red' },
            { name: 'Size', value: 'Small' }
          ]
        },
        {
          name: 'Red - Medium',
          sku: 'CTS-002-RED-M',
          price: 24.99,
          stock: 52,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Red' },
            { name: 'Size', value: 'Medium' }
          ]
        },
        {
          name: 'Red - Large',
          sku: 'CTS-002-RED-L',
          price: 24.99,
          stock: 38,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Red' },
            { name: 'Size', value: 'Large' }
          ]
        },
        {
          name: 'Blue - Small',
          sku: 'CTS-002-BLU-S',
          price: 24.99,
          stock: 0,
          status: 'Inactive',
          attributes: [
            { name: 'Color', value: 'Blue' },
            { name: 'Size', value: 'Small' }
          ]
        },
        {
          name: 'Blue - Medium',
          sku: 'CTS-002-BLU-M',
          price: 24.99,
          stock: 28,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Blue' },
            { name: 'Size', value: 'Medium' }
          ]
        },
        {
          name: 'Blue - Large',
          sku: 'CTS-002-BLU-L',
          price: 24.99,
          stock: 15,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Blue' },
            { name: 'Size', value: 'Large' }
          ]
        }
      ],
      tags: ['cotton', 'casual', 'comfortable'],
      thumbnail: '/images/tshirt.jpg',
      shortDescription: '100% cotton comfortable t-shirt available in multiple colors and sizes',
      createdAt: '2024-01-10T09:15:00Z',
      updatedAt: '2024-01-18T16:45:00Z'
    },
    {
      id: 'PRD-003',
      productName: 'Office Chair Bundle',
      productCode: 'OCB-003',
      productType: 'Bundle',
      category: { name: 'Furniture', id: 'cat-3' },
      brand: { name: 'ErgoDesk', id: 'brand-3' },
      status: 'Draft',
      mrp: 399.99,
      sellingPrice: 349.99,
      costPrice: 199.99,
      inventory: 25,
      minOrderQty: 1,
      variants: 0,
      tags: ['office', 'ergonomic', 'bundle'],
      thumbnail: '/images/office-chair.jpg',
      shortDescription: 'Ergonomic office chair with lumbar support and accessories',
      createdAt: '2024-01-12T11:00:00Z',
      updatedAt: '2024-01-19T13:30:00Z'
    },
    {
      id: 'PRD-004',
      productName: 'Smartphone Case',
      productCode: 'SPC-004',
      productType: 'Variant',
      category: { name: 'Accessories', id: 'cat-4' },
      brand: { name: 'ProtectTech', id: 'brand-4' },
      status: 'Pending Approval',
      mrp: 19.99,
      sellingPrice: 15.99,
      costPrice: 7.99,
      inventory: 300,
      minOrderQty: 1,
      variants: 8,
      variantDetails: [
        {
          name: 'iPhone 14 - Clear',
          sku: 'SPC-004-IP14-CLR',
          price: 15.99,
          stock: 45,
          status: 'Active',
          attributes: [
            { name: 'Model', value: 'iPhone 14' },
            { name: 'Color', value: 'Clear' }
          ]
        },
        {
          name: 'iPhone 14 - Black',
          sku: 'SPC-004-IP14-BLK',
          price: 15.99,
          stock: 38,
          status: 'Active',
          attributes: [
            { name: 'Model', value: 'iPhone 14' },
            { name: 'Color', value: 'Black' }
          ]
        },
        {
          name: 'iPhone 15 - Clear',
          sku: 'SPC-004-IP15-CLR',
          price: 17.99,
          stock: 52,
          status: 'Active',
          attributes: [
            { name: 'Model', value: 'iPhone 15' },
            { name: 'Color', value: 'Clear' }
          ]
        },
        {
          name: 'iPhone 15 - Black',
          sku: 'SPC-004-IP15-BLK',
          price: 17.99,
          stock: 29,
          status: 'Active',
          attributes: [
            { name: 'Model', value: 'iPhone 15' },
            { name: 'Color', value: 'Black' }
          ]
        }
      ],
      tags: ['protection', 'smartphone', 'durable'],
      thumbnail: '/images/phone-case.jpg',
      shortDescription: 'Durable smartphone case with drop protection',
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-21T10:15:00Z'
    },
    {
      id: 'PRD-005',
      productName: 'Gaming Laptop',
      productCode: 'GL-005',
      productType: 'Simple',
      category: { name: 'Computers', id: 'cat-5' },
      brand: { name: 'GameForce', id: 'brand-5' },
      status: 'Archive',
      mrp: 1299.99,
      sellingPrice: 1199.99,
      costPrice: 899.99,
      inventory: 5,
      minOrderQty: 1,
      variants: 0,
      tags: ['gaming', 'laptop', 'high-performance'],
      thumbnail: '/images/gaming-laptop.jpg',
      shortDescription: 'High-performance gaming laptop with RTX graphics',
      createdAt: '2024-01-08T16:45:00Z',
      updatedAt: '2024-01-16T12:00:00Z'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Products', count: products.length },
    { id: 'Published', label: 'Published', count: products.filter(p => p.status === 'Published').length },
    { id: 'Draft', label: 'Draft', count: products.filter(p => p.status === 'Draft').length },
    { id: 'Pending Approval', label: 'Pending Approval', count: products.filter(p => p.status === 'Pending Approval').length },
    { id: 'Archive', label: 'Archive', count: products.filter(p => p.status === 'Archive').length }
  ];

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.productName.toLowerCase().includes(filterText.toLowerCase()) ||
                         product.productCode.toLowerCase().includes(filterText.toLowerCase()) ||
                         product.brand.name.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || product.status === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  // Hover handlers
  const handleVariantHover = (variants, event) => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }

    const rect = event.target.getBoundingClientRect();
    setHoverPosition({
      x: rect.left + rect.width / 2,
      y: rect.top
    });
    setHoveredVariants(variants);
    setIsHoverDropdownVisible(true);
  };

  const handleVariantLeave = () => {
    hoverTimeoutRef.current = setTimeout(() => {
      setIsHoverDropdownVisible(false);
      setHoveredVariants(null);
    }, 300); // 300ms delay before hiding
  };

  const handleDropdownHover = () => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
  };

  const handleDropdownLeave = () => {
    setIsHoverDropdownVisible(false);
    setHoveredVariants(null);
  };

  const columns = [
    {
      name: 'Product',
      selector: row => row.productName,
      sortable: true,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="icon icon-package text-gray-400" />
          </div>
          <div>
            <div className="font-medium text-sm">{row.productName}</div>
            <div className="text-xs text-gray-500">{row.productCode}</div>
          </div>
        </div>
      ),
      width: '275px',
    },
    {
      name: 'Type',
      selector: row => row.productType,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
          row.productType === 'Simple' ? 'bg-info-500/10 text-info-500' :
          row.productType === 'Variant' ? 'bg-purple-100 text-purple-800' :
          'bg-orange-100 text-orange-800'
        }`}>
          {row.productType}
        </span>
      ),
      width: '100px',
    },
    {
      name: 'Category',
      selector: row => row.category.name,
      sortable: true,
      // width: '120px',
    },
    {
      name: 'Manufacturer',
      selector: row => row.brand.name,
      sortable: true,
      // width: '120px',
    },
    {
      name: 'Price',
      selector: row => row.sellingPrice,
      sortable: true,
      cell: (row) => (
        <div className="text-sm">
          <div className="font-medium">${row.sellingPrice}</div>
          {row.mrp !== row.sellingPrice && (
            <div className="text-xs text-gray-500 line-through">${row.mrp}</div>
          )}
        </div>
      ),
      // width: '100px',
    },
    {
      name: 'Inventory',
      selector: row => row.inventory,
      sortable: true,
      cell: (row) => (
        <span className={`font-medium ${
          row.inventory < 10 ? 'text-red-600' : 
          row.inventory < 50 ? 'text-yellow-600' : 
          'text-green-600'
        }`}>
          {row.inventory}
        </span>
      ),
      width: '100px',
    },
    {
      name: 'Variants',
      selector: row => row.variants,
      sortable: true,
      cell: (row) => (
        <div className="text-sm">
          {row.variants > 0 ? (
            <span
              className="inline-flex items-center gap-1 px-2 py-1 bg-purple-50 text-purple-700 rounded-full cursor-pointer hover:bg-purple-100 transition-colors"
              onMouseEnter={(e) => handleVariantHover(row.variantDetails || [], e)}
              onMouseLeave={handleVariantLeave}
            >
              <span className="icon icon-grid-3x3 text-xs" />
              {row.variants} variants
            </span>
          ) : (
            <span className="text-gray-500">No variants</span>
          )}

          {/* Variant Hover Dropdown */}
      <VariantHoverDropdown
        variants={hoveredVariants}
        isVisible={isHoverDropdownVisible}
        position={hoverPosition}
        onMouseEnter={handleDropdownHover}
        onMouseLeave={handleDropdownLeave}
      />
        </div>
      ),
      width: '140px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full text-nowrap ${
          row.status === 'Published' ? 'bg-green-100 text-green-800' :
          row.status === 'Draft' ? 'bg-gray-100 text-gray-800' :
          row.status === 'Pending Approval' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {row.status}
        </span>
      ),
      width: '140px',
    },
    {
      name: 'Actions',
      grow: 0,
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewProduct(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <Link
            href={`/manage-products/edit/${row.id}`}
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-pencil-line text-base" />
          </Link>
          <button
            onClick={() => handleCloneProduct(row)}
            data-tooltip-id="clone-tooltip"
            data-tooltip-content="Clone Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-info-500/10 hover:text-info-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-copy text-base" />
          </button>
          <button
            onClick={() => handleDeleteProduct(row.id)}
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-trash text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="clone-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      width: '160px',
    },
  ];

  const handleViewProduct = (product) => {
    setSelectedProduct(product);
    setIsProductDetailOpen(true);
  };

  const handleCloneProduct = (product) => {
    const clonedProduct = {
      ...product,
      id: `PRD-${Date.now()}`,
      productName: `${product.productName} (Copy)`,
      productCode: `${product.productCode}-COPY`,
      status: 'Draft'
    };
    setProducts(prev => [...prev, clonedProduct]);
    console.log('Product cloned:', clonedProduct);
  };

  const handleDeleteProduct = (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      setProducts(prev => prev.filter(p => p.id !== productId));
      console.log('Product deleted:', productId);
    }
  };

  const handleAddProduct = (productData) => {
    const newProduct = {
      ...productData,
      id: `PRD-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setProducts(prev => [...prev, newProduct]);
    console.log('Product added:', newProduct);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <div className="space-y-4 relative">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Products</h2>
          <span className="text-sm text-gray-300">
            {filteredProducts.length} of {products.length} products
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-bold text-lg" />
            Export
          </button>
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-download-simple font-bold text-lg" />
            Import
          </button>
          <button
            onClick={() => setIsAddProductOpen(true)}
            className="btn btn-primary inline-flex items-center gap-2"
          >
            <span className="icon icon-plus font-bold text-lg" />
            Add Product
          </button>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search products..."
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        />

        <DataTable
          columns={columns}
          data={filteredProducts}
          pagination
          paginationPerPage={10}
          selectableRows
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-package text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No products found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={true}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <ProductDetailModal
        isOpen={isProductDetailOpen}
        onClose={() => setIsProductDetailOpen(false)}
        product={selectedProduct}
      />

      <AddProductModal
        isOpen={isAddProductOpen}
        onClose={() => setIsAddProductOpen(false)}
        onAddProduct={handleAddProduct}
      />
    </div>
  );
};

export default ProductsTab;
