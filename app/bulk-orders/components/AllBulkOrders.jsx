'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';
import InputField from '../../components/Inputs/InputField';
import SelectField from '../../components/Inputs/SelectField';
import CommonPagination from '../../components/table/CommonPagination';
import DatePicker from 'react-datepicker';

const InfoRow = ({ label, value, className }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm text-dark-500 font-medium">{value}</span>
  </div>
);
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const EditStatusModal = ({ isOpen, onClose, order, onUpdateStatus }) => {
  const [paymentStatus, setPaymentStatus] = useState(order?.paymentStatus || '');
  const [fulfillmentStatus, setFulfillmentStatus] = useState(order?.fulfillmentStatus || '');
  const [deliveryDate, setDeliveryDate] = useState(
    order?.deliveryDate ? new Date(order.deliveryDate).toISOString().split('T')[0] : ''
  );
  const [notes, setNotes] = useState('');

  const paymentStatusOptions = ['Pending', 'Partial', 'Paid', 'Failed', 'Refunded'];
  const fulfillmentStatusOptions = ['Processing', 'Dispatched', 'Delivered', 'Cancelled'];

  const handleSubmit = (e) => {
    e.preventDefault();
    onUpdateStatus(order.id, {
      paymentStatus,
      fulfillmentStatus,
      deliveryDate: new Date(deliveryDate).toISOString(),
      notes,
      updatedAt: new Date().toISOString()
    });
    onClose();
  };

  if (!order) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Edit Status - ${order.orderId}`}
      size="sm"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          <div className="flex flex-col mb-4">
            <span className="block text-sm font-medium text-gray-700 mb-2">
              Payment Status
            </span>
            <SelectField
              label="Payment Status"
              name="paymentStatus"
              className="single-select"
              placeholder="Select payment status"
              value={paymentStatus}
              onChange={(selectedOption) =>
                setPaymentStatus(selectedOption.value)
              }
              options={paymentStatusOptions.map((status) => ({
                value: status,
                label: status
              }))}
              required={false}
            />
          </div>
          <div className="flex flex-col mb-4">
            <span className="block text-sm font-medium text-gray-700 mb-2">
              Fulfillment Status
            </span>
            <SelectField
              label="Fulfillment Status"
              name="fulfillmentStatus"
              className="single-select"
              placeholder="Select fulfillment status"
              value={fulfillmentStatus}
              onChange={(selectedOption) =>
                setFulfillmentStatus(selectedOption.value)
              }
              options={fulfillmentStatusOptions.map((status) => ({
                value: status,
                label: status
              }))}
              required={false}
            />
          </div>
          <div className="mb-4 flex flex-col custom-datepicker">
            <label className="form-label">Delivery Date</label>
            <DatePicker
              selected={deliveryDate ? new Date(deliveryDate) : null}
              onChange={(e) => setDeliveryDate(e.target.value)}
              className="form-control calendar-icon"
              dateFormat="MM/dd/yyyy"
              placeholderText="Select delivery date"
              minDate={new Date()}
            />
          </div>

          {/* <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delivery Date
            </label>
            <input
              type="date"
              value={deliveryDate}
              onChange={(e) => setDeliveryDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              required
            />
          </div> */}

          <div>
            <label htmlFor='notes' className="block text-sm font-medium text-gray-700 mb-2">
              Notes (Optional)
            </label>
            <textarea
              id='notes'
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={5}
              className="form-control"
              placeholder="Add any notes about this status update..."
            />
          </div>
        </div>

        <div className="flex justify-end gap-3 border-t border-border-color p-4">
          <button
            type="submit"
            className="btn btn-primary"
          >
            Update Status
          </button>
          <button
            type="button"
            onClick={onClose}
            className="btn btn-outline-gray"
          >
            Cancel
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

const EditOrderModal = ({ isOpen, onClose, order, onUpdateOrder }) => {
  const [formData, setFormData] = useState({
    buyerName: order?.buyerName || '',
    companyOrg: order?.companyOrg || '',
    orderValue: order?.orderValue || '',
    totalQuantity: order?.totalQuantity || '',
    orderSource: order?.orderSource || ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onUpdateOrder(order.id, {
      ...formData,
      orderValue: parseFloat(formData.orderValue),
      totalQuantity: parseInt(formData.totalQuantity),
      updatedAt: new Date().toISOString()
    });
    onClose();
  };

  if (!order) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Edit Order - ${order.orderId}`}
      size="sm"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          <InputField
            label="Buyer Name"
            name="buyerName"
            type="text"
            placeholder="Enter buyer name"
            value={formData.buyerName}
            onChange={handleInputChange}
            required={false}
          />
          <InputField
            label="Company/Organization"
            name="companyOrg"
            type="text"
            placeholder="Enter company/organization"
            value={formData.companyOrg}
            onChange={handleInputChange}
            required={false}
          />
          <InputField
            label="Order Value"
            name="orderValue"
            type="number"
            placeholder="Enter order value"
            rightText={"$"}
            rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
            inputClassName="form-control !pl-7 !pr-4"
            value={formData.orderValue}
            onChange={handleInputChange}
            required={false}
          />
          <InputField
            label="Total Quantity"
            name="totalQuantity"
            type="number"
            placeholder="Enter total quantity"
            value={formData.totalQuantity}
            onChange={handleInputChange}
            required={false}
          />
          <div className="flex flex-col mb-4">
            <span className="form-label">Order Source</span>
            <SelectField
              label="Order Source"
              name="orderSource"
              className="single-select"
              placeholder="Select order source"
              value={formData.orderSource}
              onChange={handleInputChange}
              options={[
                { value: 'Portal', label: 'Portal' },
                { value: 'Email', label: 'Email' },
                { value: 'Phone', label: 'Phone' },
                { value: 'Direct', label: 'Direct' },
                { value: 'Marketplace', label: 'Marketplace' },
              ]}
              required={false}
            />
          </div>
        </div>

        <div className="flex justify-end gap-3 border-t border-border-color p-4">
          <button
            type="submit"
            className="btn btn-primary"
          >
            Update Order
          </button>
          <button
            type="button"
            onClick={onClose}
            className="btn btn-outline-gray"
          >
            Cancel
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

const OrderDetailModal = ({ isOpen, onClose, order, onEditStatus, onEditOrder }) => {
  if (!order) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Bulk Order Details - ${order.orderId}`}
      size="md"
    >
      <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Order Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Order Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Order ID" value={order.orderId} />
            <InfoRow label="Order Date" value={new Date(order.orderDate).toLocaleDateString()} />
            <InfoRow label="Buyer Name" value={order.buyerName} />
            <InfoRow label="Company/Organization" value={order.companyOrg || 'N/A'} />
            <InfoRow label="Total Quantity" value={`${order.totalQuantity.toLocaleString()} units`} />
            <InfoRow label="Order Value" value={`$${order.orderValue.toLocaleString()}`} />
          </div>
        </div>
        <hr/>
        {/* Products Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Products</h4>
          <div className="space-y-3">
            {order.products.map((product, index) => (
              <div key={index} className="flex justify-between items-center p-3 border border-border-color rounded-lg">
                <div>
                  <p className="font-medium text-sm">{product.name}</p>
                  <p className="text-xs text-gray-300">SKU: {product.sku}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{product.quantity.toLocaleString()} units</p>
                  <p className="text-sm text-gray-500">${product.unitPrice.toFixed(2)} each</p>
                </div>
              </div>
            ))}
          </div>
        </div>
        <hr/>

        {/* Status and Delivery */}
        <div className="">
          <h4 className="font-semibold mb-3">Status & Delivery</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-1 items-start">
              <span className="text-sm text-gray-500/60 font-normal">Payment Status</span>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                order.paymentStatus === 'Paid' ? 'bg-green-100 text-green-800' :
                order.paymentStatus === 'Partial' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {order.paymentStatus}
              </span>
            </div>
            <div className="flex flex-col gap-1 items-start">
              <span className="text-sm text-gray-500/60 font-normal">Fulfillment Status</span>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                order.fulfillmentStatus === 'Delivered' ? 'bg-green-100 text-green-800' :
                order.fulfillmentStatus === 'Dispatched' ? 'bg-purple-100 text-purple-800' :
                order.fulfillmentStatus === 'Processing' ? 'bg-info-500/20 text-info-500' :
                'bg-gray-100 text-gray-800'
              }`}>
                {order.fulfillmentStatus}
              </span>
            </div>
            <InfoRow label="Delivery Date" value={new Date(order.deliveryDate).toLocaleDateString()} />
            <InfoRow label="Order Source" value={order.orderSource} />
          </div>
        </div>
      </div>
      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        <button
          onClick={() => onEditStatus(order)}
          className="btn btn-primary"
        >
          Edit Status
        </button>
        <button
          onClick={() => onEditOrder(order)}
          className="btn btn-outline-gray"
        >
          Edit Order
        </button>
        <button className="btn btn-gray">Generate Invoice</button>
        <button className="btn btn-outline-danger">Cancel Order</button>
      </div>
    </BaseOffCanvas>
  );
};

const AllBulkOrders = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [isOrderDetailOpen, setIsOrderDetailOpen] = useState(false);
  const [isEditStatusOpen, setIsEditStatusOpen] = useState(false);
  const [isEditOrderOpen, setIsEditOrderOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    orderId: true,
    orderDate: true,
    buyerName: true,
    companyOrg: true,
    products: true,
    totalQuantity: true,
    orderValue: true,
    paymentStatus: true,
    fulfillmentStatus: true,
    deliveryDate: true,
    orderSource: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [orders, setOrders] = useState([
    {
      id: 'BO-001',
      orderId: 'BO-001',
      orderDate: '2024-01-15T10:30:00Z',
      buyerName: 'ABC Corporation',
      companyOrg: 'ABC Corp Ltd.',
      products: [
        { name: 'Industrial Printer', sku: 'IP-001', quantity: 50, unitPrice: 1200.00 },
        { name: 'Paper Rolls', sku: 'PR-002', quantity: 500, unitPrice: 25.00 }
      ],
      totalQuantity: 550,
      orderValue: 72500,
      paymentStatus: 'Paid',
      fulfillmentStatus: 'Processing',
      deliveryDate: '2024-01-25T00:00:00Z',
      orderSource: 'Portal'
    },
    {
      id: 'BO-002',
      orderId: 'BO-002',
      orderDate: '2024-01-14T14:20:00Z',
      buyerName: 'XYZ Manufacturing',
      companyOrg: 'XYZ Manufacturing Inc.',
      products: [
        { name: 'Steel Pipes', sku: 'SP-003', quantity: 200, unitPrice: 150.00 }
      ],
      totalQuantity: 200,
      orderValue: 30000,
      paymentStatus: 'Partial',
      fulfillmentStatus: 'Dispatched',
      deliveryDate: '2024-01-20T00:00:00Z',
      orderSource: 'Salesperson'
    },
    {
      id: 'BO-003',
      orderId: 'BO-003',
      orderDate: '2024-01-13T09:15:00Z',
      buyerName: 'Tech Solutions Ltd',
      companyOrg: 'Tech Solutions Limited',
      products: [
        { name: 'Laptops', sku: 'LP-004', quantity: 100, unitPrice: 800.00 },
        { name: 'Monitors', sku: 'MN-005', quantity: 100, unitPrice: 300.00 }
      ],
      totalQuantity: 200,
      orderValue: 110000,
      paymentStatus: 'Paid',
      fulfillmentStatus: 'Delivered',
      deliveryDate: '2024-01-18T00:00:00Z',
      orderSource: 'External'
    },
    {
      id: 'BO-004',
      orderId: 'BO-004',
      orderDate: '2024-01-12T16:45:00Z',
      buyerName: 'Global Retail Chain',
      companyOrg: 'Global Retail Chain PLC',
      products: [
        { name: 'Office Chairs', sku: 'OC-006', quantity: 300, unitPrice: 120.00 }
      ],
      totalQuantity: 300,
      orderValue: 36000,
      paymentStatus: 'Unpaid',
      fulfillmentStatus: 'Pending',
      deliveryDate: '2024-01-30T00:00:00Z',
      orderSource: 'Portal'
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'partial':
        return 'bg-yellow-100 text-yellow-800';
      case 'unpaid':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'processing':
        return 'bg-info-500/20 text-info-500';
      case 'dispatched':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Order ID',
      selector: (row) => row.orderId,
      sortable: true,
      omit: !displayProperties.orderId,
    },
    {
      name: 'Order Date',
      selector: (row) => row.orderDate,
      sortable: true,
      omit: !displayProperties.orderDate,
      cell: (row) => new Date(row.orderDate).toLocaleDateString(),
    },
    {
      name: 'Buyer Name',
      selector: (row) => row.buyerName,
      sortable: true,
      omit: !displayProperties.buyerName,
    },
    {
      name: 'Company / Org',
      selector: (row) => row.companyOrg,
      sortable: true,
      omit: !displayProperties.companyOrg,
      cell: (row) => row.companyOrg || '-',
    },
    {
      name: 'Product(s)',
      selector: (row) => row.products,
      omit: !displayProperties.products,
      cell: (row) => (
        <div className="text-sm">
          {row.products.length === 1 ? (
            <div>{row.products[0].name}</div>
          ) : (
            <div>{row.products.length} products</div>
          )}
        </div>
      ),
    },
    {
      name: 'Total Quantity',
      selector: (row) => row.totalQuantity,
      sortable: true,
      omit: !displayProperties.totalQuantity,
      cell: (row) => `${row.totalQuantity.toLocaleString()} units`,
    },
    {
      name: 'Order Value',
      selector: (row) => row.orderValue,
      sortable: true,
      omit: !displayProperties.orderValue,
      cell: (row) => `$${row.orderValue.toLocaleString()}`,
    },
    {
      name: 'Payment Status',
      selector: (row) => row.paymentStatus,
      sortable: true,
      omit: !displayProperties.paymentStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.paymentStatus)}`}>
          {row.paymentStatus}
        </span>
      ),
    },
    {
      name: 'Fulfillment Status',
      selector: (row) => row.fulfillmentStatus,
      sortable: true,
      omit: !displayProperties.fulfillmentStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.fulfillmentStatus)}`}>
          {row.fulfillmentStatus}
        </span>
      ),
    },
    {
      name: 'Delivery Date',
      selector: (row) => row.deliveryDate,
      sortable: true,
      omit: !displayProperties.deliveryDate,
      cell: (row) => new Date(row.deliveryDate).toLocaleDateString(),
    },
    {
      name: 'Order Source',
      selector: (row) => row.orderSource,
      sortable: true,
      omit: !displayProperties.orderSource,
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewOrder(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <button
            onClick={() => handleEditStatus(row)}
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit Status"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base">
            {/* Edit Status */}
            <span className="icon icon-pencil-line text-base" />
          </button>
          <button
            data-tooltip-id="invoice-tooltip"
            data-tooltip-content="Generate Invoice"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-success-500/10 hover:text-success-500 rounded-lg cursor-pointer transition-base">
            {/* Invoice */}
            <span className="icon icon-file-text text-base" />
          </button>
          <button
            onClick={() => handleCancelOrder(row.id)}
            data-tooltip-id="cancel-tooltip"
            data-tooltip-content="Cancel Order"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base">
            {/* Cancel */}
            <span className="icon icon-x text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="invoice-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="cancel-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // allowOverflow: true,
      // button: true,
      width: '160px',
    },
  ];

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setIsOrderDetailOpen(true);
  };

  const handleEditStatus = (order) => {
    setSelectedOrder(order);
    setIsEditStatusOpen(true);
  };

  const handleEditOrder = (order) => {
    setSelectedOrder(order);
    setIsEditOrderOpen(true);
  };

  const handleUpdateStatus = (orderId, statusData) => {
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === orderId
          ? {
              ...order,
              paymentStatus: statusData.paymentStatus,
              fulfillmentStatus: statusData.fulfillmentStatus,
              deliveryDate: statusData.deliveryDate,
              lastUpdated: statusData.updatedAt,
              statusNotes: statusData.notes
            }
          : order
      )
    );
    // Show success message (in real app, this would be an API call)
    console.log('Order status updated successfully:', { orderId, statusData });
  };

  const handleUpdateOrder = (orderId, updatedData) => {
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === orderId
          ? { ...order, ...updatedData }
          : order
      )
    );
    // Show success message (in real app, this would be an API call)
    console.log('Order updated successfully:', { orderId, updatedData });
  };

  const handleCancelOrder = (orderId) => {
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === orderId
          ? {
              ...order,
              paymentStatus: 'Refunded',
              fulfillmentStatus: 'Cancelled',
              lastUpdated: new Date().toISOString()
            }
          : order
      )
    );
    // Show success message (in real app, this would be an API call)
    console.log('Order cancelled successfully:', orderId);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredOrders = orders.filter((order) =>
    order.buyerName.toLowerCase().includes(filterText.toLowerCase()) ||
    order.orderId.toLowerCase().includes(filterText.toLowerCase()) ||
    order.companyOrg?.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium', grow: 1 },
              { size: 'medium' },
              { size: 'medium' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredOrders}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Order Detail Modal */}
      <OrderDetailModal
        isOpen={isOrderDetailOpen}
        onClose={() => setIsOrderDetailOpen(false)}
        order={selectedOrder}
        onEditStatus={handleEditStatus}
        onEditOrder={handleEditOrder}
      />

      {/* Edit Status Modal */}
      <EditStatusModal
        isOpen={isEditStatusOpen}
        onClose={() => setIsEditStatusOpen(false)}
        order={selectedOrder}
        onUpdateStatus={handleUpdateStatus}
      />

      {/* Edit Order Modal */}
      <EditOrderModal
        isOpen={isEditOrderOpen}
        onClose={() => setIsEditOrderOpen(false)}
        order={selectedOrder}
        onUpdateOrder={handleUpdateOrder}
      />
    </div>
  );
};

export default AllBulkOrders;
